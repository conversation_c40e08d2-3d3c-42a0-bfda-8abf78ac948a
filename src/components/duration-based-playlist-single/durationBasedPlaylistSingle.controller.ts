import {
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Query,
  Req,
  Res,
  UseInterceptors
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { FastifyReply, FastifyRequest } from 'fastify';
import { LogLevel } from 'adpod-tools';
import { v4 as uuid } from 'uuid';
import { isEmptyVast } from '../../scripts/vast/isEmptyVast';
import { setPlaylistResHeaders } from '../../scripts/playlist/setPlaylistResHeaders';
import { RequestMacroParams } from '../../scripts/configuration/injectReqParams/requestMacroParams';
import { GetDurationBasedPlaylistQueryDto } from './dtos/getDurationBasedPlaylistQuery.dto';
import { Protocol } from '../../models/protocol.model';
import { getActiveSpan, getActiveTraceId } from '../../libs/logging/trace';
import { extractParamsFromRequest } from '../../scripts/utils/extractParamsFromRequest';
import { TimeoutInterceptor } from '../../libs/interceptors/timeout.interceptor';
import { ApiResponses, ApiHeaders } from './durationBasedPlaylistSingle.doc.decorator';
import { IDurationBasedPlaylistSingleService } from './durationBasedPlaylistSingle.service';
import logger from '../../libs/logging/logger';
import { sendResponseLogsToSnowflake } from '../../scripts/playlist/sendResponseLogsToSnowflake';
import { WorkerConfigType } from '../../models/workerConfig';
import { sendRequestLogsToSnowflake } from '../../scripts/playlist/sendRequestLogsToSnowflake';
import { ICacheProvider } from '../../libs/caching';

@Controller('api/playlist')
@ApiTags('playlist')
@UseInterceptors(TimeoutInterceptor)
export class DurationBasedPlaylistSingleController {
  constructor(
    private readonly durationBasedService: IDurationBasedPlaylistSingleService,
    private readonly localCache: ICacheProvider
  ) {}

  @Get('duration-based/get')
  @ApiHeaders()
  @ApiResponses()
  async get(
    @Req() req: FastifyRequest,
    @Query() queryParams: GetDurationBasedPlaylistQueryDto,
    @Res() res: FastifyReply
  ): Promise<void> {
    try {
      const { v, uid, cust_params, ch, output, mode, duration } = queryParams;

      const span = getActiveSpan();

      const { ipLogging, snowFlakeExcludedConfigs } =
        await this.getFreeWheelDurationWorkerConfig(v);

      const requestProtocol = (req.raw.headers['x-tvn-links-response-proto'] ||
        req.raw.headers['x-forwarded-proto'] ||
        req.protocol) as Protocol;
      const sessionId = uuid();
      const { apmIp, reqIp, requestUrl, rawHeaders } = extractParamsFromRequest(req);
      const requestIPforSnowflake = ipLogging ? apmIp : '';

      const routeParams = new RequestMacroParams(
        uid,
        requestProtocol,
        undefined,
        cust_params,
        ch
      );

      const requestDetails = {
        ...queryParams,
        rawHeaders,
        requestUrl,
        reqIP: reqIp
      };

      logger('DURATION_BASED_REQUEST_DETAILS', requestDetails, LogLevel.dev);

      sendRequestLogsToSnowflake(
        sessionId,
        v,
        ch,
        snowFlakeExcludedConfigs,
        requestUrl,
        uid,
        requestIPforSnowflake
      );

      if (span !== null) {
        span.setTag('tag-headers', req.headers);
        span.setTag('tag-rawHeaders', req.raw.headers);
        span.setTag('tag-requestUrl', requestUrl);
        span.setTag('tag-reqIP', reqIp);
        span.setTag('tag-version', v);
        span.setTag('tag-traceId', getActiveTraceId());
      }

      const { playlist, emptyVastReason, playlistInfo, requestLog } =
        await this.durationBasedService.handle(
          ch,
          routeParams,
          v,
          duration,
          uid,
          output,
          req.raw.headers,
          mode
        );

      logger(
        'GET_PLAYLIST_STATS',
        {
          ...sendResponseLogsToSnowflake(
            requestLog,
            res.elapsedTime,
            apmIp,
            req.headers,
            emptyVastReason,
            playlistInfo,
            snowFlakeExcludedConfigs,
            v,
            sessionId,
            ipLogging
          )
        },
        LogLevel.dev
      );

      setPlaylistResHeaders(
        res,
        isEmptyVast(playlist),
        req.raw.headers['x-tvn-links-response-proto'],
        req.raw.headers['x-forwarded-proto'],
        requestProtocol
      );

      res.send(playlist);
    } catch (e: unknown) {
      logger('ERROR_UNKNOWN_SERVER_ERROR', { e }, LogLevel.error);
      if (e instanceof HttpException) throw e;
      if (e instanceof Error) {
        throw new HttpException({ ...e }, HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }
  }

  private async getFreeWheelDurationWorkerConfig(version: string) {
    const workerConfig = await this.localCache.get<WorkerConfigType>('workerConfig');
    const frewheelConfig = workerConfig?.freeWheelDuration[version];

    return {
      filler: frewheelConfig?.filler ?? false,
      ipLogging: workerConfig?.snowflake?.ipLogging ?? false,
      snowFlakeExcludedConfigs: workerConfig?.snowflake?.snowFlakeEnabledConfigs ?? []
    };
  }
}
