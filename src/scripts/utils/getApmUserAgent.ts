import { FastifyRequest } from 'fastify';
import { transformToString } from './getIp';
import { ApmUserAgentsType, UaType, UserAgentSources } from './requestParams.interface';

export function getApmUserAgents(
  { raw: { headers } }: FastifyRequest,
  queryUserAgent?: string
): ApmUserAgentsType {
  const uaArray: UaType[] = [
    { userAgent: queryUserAgent, uaSource: 'query-param-ua' },
    { userAgent: headers['x-device-user-agent'], uaSource: 'header-x-device-user-agent' },
    { userAgent: headers['user-agent'], uaSource: 'header-user-agent' }
  ].map(({ userAgent, uaSource }) => ({
    userAgent: transformToString(userAgent) || 'empty',
    uaSource
  }));

  const selectedUA = uaArray.find(({ userAgent }) => userAgent !== 'empty') ?? {
    userAgent: 'empty',
    uaSource: 'empty'
  };

  const userAgentSources: UserAgentSources = uaArray.reduce((acc, { userAgent, uaSource }) => {
    acc[uaSource] = userAgent;
    return acc;
  }, {});

  return { selectedUA, userAgentSources };
}

// TODO: make tests
