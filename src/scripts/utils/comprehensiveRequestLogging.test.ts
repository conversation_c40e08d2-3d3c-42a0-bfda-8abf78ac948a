// import { FastifyRequest } from 'fastify';
// import { extractParamsFromRequest } from './extractParamsFromRequest';
// import * as loggerService from '../../libs/logging/logger';

// const mockLogger = jest.fn();
// jest.spyOn(loggerService, 'default').mockImplementation(mockLogger);

// describe('Comprehensive Request Logging', () => {
//   const createMockRequest = (
//     options: {
//       query?: object;
//       headers?: object;
//       remoteAddress?: string;
//       url?: string;
//       method?: string;
//     } = {}
//   ): FastifyRequest => {
//     const { query = {}, headers = {}, remoteAddress, url = '/test', method = 'GET' } = options;
//     return {
//       query,
//       headers,
//       socket: { remoteAddress },
//       raw: { headers, url, method }
//     } as FastifyRequest;
//   };

//   describe('extractAllIpSources', () => {
//     it('should extract all IP sources correctly', () => {
//       const req = createMockRequest({
//         query: { ip: '*******' },
//         headers: {
//           'x-device-ip': '*******',
//           'x-forwarded-for': '*******'
//         },
//         remoteAddress: '*******'
//       });

//       const result = extractAllIpSources(req);

//       expect(result).toEqual({
//         queryParam: '*******',
//         xDeviceIp: '*******',
//         xForwardedFor: '*******',
//         directConnection: '*******'
//       });
//     });

//     it('should return "empty" for missing IP sources', () => {
//       const req = createMockRequest();

//       const result = extractAllIpSources(req);

//       expect(result).toEqual({
//         queryParam: 'empty',
//         xDeviceIp: 'empty',
//         xForwardedFor: 'empty',
//         directConnection: 'empty'
//       });
//     });

//     it('should handle array query parameters', () => {
//       const req = createMockRequest({
//         query: { ip: ['*******', '*******'] }
//       });

//       const result = extractAllIpSources(req);

//       expect(result.queryParam).toBe('*******,*******');
//     });
//   });

//   describe('extractAllUserAgentSources', () => {
//     it('should extract all User Agent sources correctly', () => {
//       const req = createMockRequest({
//         query: { ua: 'query-user-agent' },
//         headers: {
//           'x-device-user-agent': 'device-user-agent',
//           'user-agent': 'standard-user-agent'
//         }
//       });

//       const result = extractAllUserAgentSources(req);

//       expect(result).toEqual({
//         queryParam: 'query-user-agent',
//         xDeviceUserAgent: 'device-user-agent',
//         userAgent: 'standard-user-agent'
//       });
//     });

//     it('should return "empty" for missing User Agent sources', () => {
//       const req = createMockRequest();

//       const result = extractAllUserAgentSources(req);

//       expect(result).toEqual({
//         queryParam: 'empty',
//         xDeviceUserAgent: 'empty',
//         userAgent: 'empty'
//       });
//     });

//     it('should handle array headers correctly', () => {
//       const req = createMockRequest({
//         headers: {
//           'x-device-user-agent': ['first-ua', 'second-ua'],
//           'user-agent': ['first-standard-ua', 'second-standard-ua']
//         }
//       });

//       const result = extractAllUserAgentSources(req);

//       expect(result.xDeviceUserAgent).toBe('first-ua,second-ua');
//       expect(result.userAgent).toBe('first-standard-ua,second-standard-ua');
//     });
//   });

//   describe('getSelectedValues', () => {
//     it('should return selected IP and User Agent values', () => {
//       const req = createMockRequest({
//         query: { ip: '*******', ua: 'test-user-agent' },
//         headers: {
//           'x-device-ip': '*******',
//           'x-device-user-agent': 'device-user-agent'
//         }
//       });

//       const result = getSelectedValues(req);

//       expect(result.ip).toBe('*******');
//       expect(result.userAgent).toBe('test-user-agent');
//     });

//     it('should handle missing query parameters', () => {
//       const req = createMockRequest({
//         headers: {
//           'x-device-ip': '*******',
//           'x-device-user-agent': 'device-user-agent'
//         }
//       });

//       const result = getSelectedValues(req);

//       expect(result.ip).toBe('*******'); // Falls back to header
//       expect(result.userAgent).toBe('device-user-agent'); // Falls back to header
//     });
//   });

//   describe('logComprehensiveRequestData', () => {
//     it('should return a request ID', () => {
//       const req = createMockRequest();

//       expect(() => extractParamsFromRequest(req)).not.toThrow();
//       expect(mockLogger).toHaveBeenCalledWith(
//         'COMPREHENSIVE_REQUEST_LOGGING',
//         {
//           sessionId: expect.any(String),
//           timestamp: expect.any(Number),
//           url: '/test',
//           method: 'GET',
//           ipSources: expect.objectContaining({
//             queryParam: 'empty',
//             xDeviceIp: 'empty',
//             xForwardedFor: 'empty',
//             directConnection: 'empty'
//           }),
//           userAgentSources: expect.objectContaining({
//             queryParam: 'empty',
//             xDeviceUserAgent: 'empty',
//             userAgent: 'empty'
//           }),
//           selectedValues: expect.objectContaining({
//             ip: 'empty',
//             userAgent: 'empty'
//           })
//         },
//         'info'
//       );
//     });
//   });
// });
