import { Prettify } from 'adpod-tools/dist/types/common.type';
import { IncomingHttpHeaders } from 'http2';

export type SelectedValues = Prettify<APMIpType & ReqIpType & UaType>;

export type IpAndUaSources = {
  ipSources: IPSources;
  userAgentSources: UserAgentSources;
  selectedValues: SelectedValues;
};

export type RequestDetails = Prettify<
  SelectedValues & {
    rawHeaders: IncomingHttpHeaders;
    requestUrl?: string;
  }
>;

export type APMIpType = { apmIp: string; apmSource: string };
export type ReqIpType = { reqIp: string; reqSource: string };
export type IpType = { ip: string; source: string };
export type IPSources = Record<string, string>;
export type IpsAndSources = Prettify<APMIpType & ReqIpType>;
export type ClientAndApmIps = { selectedIps: IpsAndSources; ipSources: IPSources };

export type UaType = {
  userAgent: string;
  uaSource: string;
};

export type UserAgentSources = Record<string, string>;
export type ApmUserAgentsType = {
  selectedUA: UaType;
  userAgentSources: UserAgentSources;
};
