import { FastifyRequest } from 'fastify';
import is from './is';
import { IncomingHttpHeaders } from 'node:http';
import { IpType, ClientAndApmIps, IpsAndSources, IPSources } from './requestParams.interface';

/**
 * Headers that contain APM specific IP addresses
 */
const APM_HEADERS = ['x-device-ip'] as const;

/**
 * Headers that contain client IP addresses
 */
const CLIENT_HEADERS = ['x-client-ip'] as const;

/**
 * List of other possible IP headers in order of preference
 * These are checked as fallbacks when primary headers don't contain valid IPs
 */
const FALLBACK_IP_HEADERS = [
  'x-forwarded-for',
  'cf-connecting-ip',
  'fastly-client-ip',
  'true-client-ip',
  'x-real-ip',
  'x-cluster-client-ip',
  'x-forwarded',
  'forwarded-for',
  'forwarded',
  'x-appengine-user-ip',
  'Cf-Pseudo-IPv4'
] as const;

/**
 * Transforms IP value(s) to a comma-separated string.
 * If the input is already a string, it is returned as is.
 * @param ips - Single IP string or array of IP strings
 * @returns Comma-separated string of IPs
 */
export const transformToString = (ips: string | string[] | undefined): string => {
  return Array.isArray(ips) ? ips.join(',') : (ips ?? '');
};

/**
 * Extracts and validates the first valid IP address from a header value.
 * Handles comma-separated IPs and IPv4 addresses with ports.
 * @param headerValue - Header value that may contain IP address(es)
 * @returns First valid IP address found, or undefined if none found
 */
function findIp(headerValue: string | string[] | null | undefined): string | undefined {
  if (!headerValue || headerValue.length === 0) {
    return undefined;
  }

  const headerAsString = transformToString(headerValue);

  const ips = headerAsString.split(',').map((headerPart) => {
    const ip = headerPart.trim();

    if (ip.includes(':')) {
      const splitted = ip.split(':');

      if (splitted.length === 2) {
        return splitted[0];
      }
    }

    return ip;
  });

  return ips.find((ip) => is.ip(ip));
}

/**
 * Scans through a list of headers to find the first valid IP address.
 * @param headers - HTTP headers object
 * @param headersToScan - Array of header names to check
 * @returns First valid IP address found, or undefined if none found
 */
const scanHeaders = (
  headers: IncomingHttpHeaders,
  headersToScan: readonly string[]
): IpType => {
  for (const header of headersToScan) {
    const foundIp = findIp(headers[header]);

    if (foundIp) {
      return { ip: foundIp, source: header };
    }
  }
  return { ip: 'empty', source: 'empty' };
};

/**
 * Attempts to extract IP address from fallback headers and socket information.
 * @param req - Fastify request object or raw request
 * @returns IP address from fallback sources, or undefined if none found
 */
const getIpFromOtherHeaders = (req: FastifyRequest | FastifyRequest['raw']): IpType => {
  if (req.headers) {
    const ipSource = scanHeaders(req.headers, FALLBACK_IP_HEADERS);

    if (ipSource.ip !== 'empty') {
      return ipSource;
    }
  }

  if (is.ip(req.socket?.remoteAddress)) {
    return { ip: req.socket.remoteAddress, source: 'socket' };
  }

  if ('raw' in req) {
    return getIpFromOtherHeaders(req.raw);
  }

  return { ip: 'empty', source: 'empty' };
};

export function getIps(req: FastifyRequest, queryIp?: string): ClientAndApmIps {
  let apmIp: string | undefined;
  let apmSource: string | undefined;
  let reqIp: string | undefined;
  let reqSource: string | undefined;

  if (queryIp && is.ip(queryIp)) {
    apmIp = queryIp;
    apmSource = 'query-param-ip';
  } else if (req.raw?.headers) {
    const ipSource = scanHeaders(req.raw.headers, APM_HEADERS);

    apmIp = ipSource.ip;
    apmSource = ipSource.source;
  }

  if (req.headers) {
    const ipSource = scanHeaders(req.headers, CLIENT_HEADERS);

    reqIp = ipSource.ip;
    reqSource = ipSource.source;
  }

  if (!apmIp || !reqIp || !reqSource || !apmSource) {
    const { ip, source } = getIpFromOtherHeaders(req);

    if (!apmIp || !apmSource) {
      apmIp = ip;
      apmSource = source;
    }
    if (!reqIp || !reqSource) {
      reqIp = ip;
      reqSource = source;
    }
  }

  const selectedIps: IpsAndSources = {
    apmIp,
    apmSource,
    reqIp,
    reqSource
  };

  const ipArray: IpType[] = [
    { ip: queryIp, source: 'query-param-ip' },
    { ip: req.raw?.headers['x-device-ip'], source: 'x-device-ip' },
    { ip: req.raw?.headers['x-forwarded-for'], source: 'x-forwarded-for' },
    {
      ip: req.socket?.remoteAddress || req.raw?.socket?.remoteAddress,
      source: 'socket-remoteAddress'
    }
  ].map(({ ip, source }) => ({ ip: transformToString(ip) || 'empty', source }));

  const ipSources = ipArray.reduce<IPSources>((acc, { ip, source }) => {
    acc[source] = ip;
    return acc;
  }, {});

  return { selectedIps, ipSources };
}
// TODO: adjust existing tests
