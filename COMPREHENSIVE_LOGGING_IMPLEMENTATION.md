# Comprehensive IP Address and User Agent Logging Implementation

## Overview

This implementation provides comprehensive logging of IP addresses and User Agent strings for all incoming requests in the TVN ad tech application. The system captures data from multiple sources and logs both the raw values and the selected values used by the application.

## Implementation Components

### 1. Core Logging Utility (`src/scripts/utils/comprehensiveRequestLogging.ts`)

**Key Functions:**

- `extractAllIpSources()` - Extracts IP addresses from all sources
- `extractAllUserAgentSources()` - Extracts User Agent strings from all sources
- `getSelectedValues()` - Gets the final IP/UA values used by the application
- `logComprehensiveRequestData()` - Main logging function that generates request ID and logs all data

**IP Address Sources Captured:**

1. Query parameter `ip`
2. `X-DEVICE-IP` header
3. `X-FORWARDED-FOR` header
4. Direct connection IP (socket.remoteAddress)

**User Agent Sources Captured:**

1. Query parameter `ua`
2. `X-DEVICE-USER-AGENT` header
3. `USER-AGENT` header

**Logging Format:**

```typescript
{
  requestId: string,           // Unique UUID for request correlation
  timestamp: number,           // Unix timestamp
  url: string,                 // Request URL
  method: string,              // HTTP method
  ipSources: {
    queryParam: string,        // IP from query param or "empty"
    xDeviceIp: string,         // IP from X-DEVICE-IP header or "empty"
    xForwardedFor: string,     // IP from X-FORWARDED-FOR header or "empty"
    directConnection: string   // IP from socket connection or "empty"
  },
  userAgentSources: {
    queryParam: string,        // UA from query param or "empty"
    xDeviceUserAgent: string,  // UA from X-DEVICE-USER-AGENT header or "empty"
    userAgent: string          // UA from USER-AGENT header or "empty"
  },
  selectedValues: {
    ip: string | undefined,    // Final IP value used by application
    userAgent: string | undefined  // Final UA value used by application
  }
}
```

### 2. Direct Controller Integration

**Purpose:**

- Called directly from each controller method that handles requests
- Generates unique session ID for correlation
- Calls comprehensive logging utility
- Replaces existing session ID generation with comprehensive logging

**Integration:**

- Added to all main request-handling controllers
- Called at the beginning of each controller method
- Non-blocking - uses `setImmediate()` for async logging

### 3. Request Context Enhancement (`src/libs/request-context/request-context.ts`)

**Enhancement:**

- Added `sessionId?: string` to RequestContext type
- Allows correlation of logs across the application lifecycle

### 4. Controller Integration

**Controllers Updated:**

- `playlist-single` controller - `/api/playlist/get` and `/api/playlist/schedule-based/get`
- `playlist-multiple` controller - `/api/playlist/prefetch` and `/api/playlist/schedule-based/prefetch`
- `duration-based-playlist-single` controller - `/api/playlist/duration-based/get`

**Implementation:**

- Comprehensive logging call replaces existing `uuid()` session ID generation
- Called immediately after extracting query parameters
- Session ID returned by logging function is used throughout request lifecycle

## Key Features

### 1. Comprehensive Data Capture

- Captures ALL available IP and User Agent sources
- Logs "empty" for any missing values
- No data loss - every source is recorded

### 2. Selected Value Tracking

- Logs the final IP/UA values chosen by existing application logic
- Uses existing `getApmIp()` function for IP selection
- Implements safe User Agent extraction with proper type handling

### 3. Request Correlation

- Generates unique UUID for each request
- Stores in request context for use throughout request lifecycle
- Enables correlation of logs across different application components

### 4. Performance Optimized

- Asynchronous logging using `setImmediate()`
- Non-blocking request processing
- Error handling prevents middleware from breaking requests

### 5. Type Safety

- Full TypeScript implementation
- Proper handling of header arrays (`string | string[]`)
- Safe extraction functions for all data sources

## Usage Examples

### Accessing Session ID in Controllers

```typescript
import { requestContextStorage } from '../libs/request-context';

// In any controller or service
const context = requestContextStorage.getStore();
const sessionId = context?.sessionId;
```

### Controller Implementation Example

```typescript
// In controller method
const sessionId = logComprehensiveRequestData(req);
// sessionId is now available for use throughout the request lifecycle
```

### Log Message Format

The comprehensive logging generates log messages with the key `COMPREHENSIVE_REQUEST_LOGGING`:

```
COMPREHENSIVE_REQUEST_LOGGING: {
  sessionId: "550e8400-e29b-41d4-a716-446655440000",
  timestamp: 1703123456789,
  url: "/api/playlist/get?bid=123&ch=tvn&ip=*******&ua=Mozilla...",
  method: "GET",
  ipSources: {
    queryParam: "*******",
    xDeviceIp: "*******",
    xForwardedFor: "**********",
    directConnection: "*************"
  },
  userAgentSources: {
    queryParam: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)...",
    xDeviceUserAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0...)...",
    userAgent: "PostmanRuntime/7.39.0"
  },
  selectedValues: {
    ip: "*******",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)..."
  }
}
```

## Testing

Comprehensive test suite included in `src/scripts/utils/comprehensiveRequestLogging.test.ts`:

- Tests all extraction functions
- Validates handling of missing values
- Tests array parameter/header handling
- Verifies error handling

## Error Handling

- All logging operations wrapped in try-catch blocks
- Errors logged separately without breaking request processing
- Graceful degradation - missing data logged as "empty"
- Request processing continues even if logging fails

## Performance Impact

- Minimal performance impact due to async logging
- No blocking operations in request path
- Efficient data extraction using existing utility functions
- Memory efficient - no large object retention

## Monitoring and Analysis

The structured logging format enables:

- Easy parsing and analysis of IP/UA data
- Request correlation across application components
- Monitoring of data source availability
- Analysis of IP/UA selection patterns
- Debugging of request routing and processing issues
